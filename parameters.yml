Resources:
  # Direct Parameter Store parameter for OAuth credentials
  AppHeroOAuthCredentials:
    Type: AWS::SSM::Parameter
    Properties:
      Name: /apphero/${self:provider.stage}/agent-oap-oauth-tokens
      Type: SecureString
      Value: 'PLACEHOLDER_VALUE_UPDATE_AFTER_DEPLOYMENT'
      Description: 'OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth'
      Tags:
        ENVIRONMENT: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        TEAM: 'EIP Development Team'
        PROJECT: 'APPHERO'
        PURPOSE: 'OAuth Authentication'
